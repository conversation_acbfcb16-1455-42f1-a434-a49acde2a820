<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page isELIgnored="false" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出版社信息管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .stats {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        th {
            padding: 20px;
            text-align: left;
            font-weight: 600;
            font-size: 1.1em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        tbody tr {
            transition: all 0.3s ease;
        }

        tbody tr:nth-child(even) {
            background-color: #f8f9ff;
        }

        tbody tr:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: scale(1.02);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        td {
            padding: 18px 20px;
            border-bottom: 1px solid #eee;
            font-size: 1em;
        }

        .publisher-id {
            font-weight: bold;
            color: #667eea;
            font-size: 1.1em;
        }

        tbody tr:hover .publisher-id {
            color: white;
        }

        .publisher-name {
            font-weight: 600;
            color: #333;
        }

        tbody tr:hover .publisher-name {
            color: white;
        }

        .contact-person {
            color: #666;
            font-style: italic;
        }

        tbody tr:hover .contact-person {
            color: white;
        }

        .mobile-number {
            color: #28a745;
            font-weight: 500;
            font-family: 'Courier New', monospace;
        }

        tbody tr:hover .mobile-number {
            color: white;
        }

        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
        }

        .back-btn {
            display: inline-block;
            margin-top: 20px;
            padding: 12px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .container {
            animation: fadeInUp 0.8s ease;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>📚出版社信息管理系统</h1>
            <p>Publisher Information Management System</p>
        </div>

        <div class="content">
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">${publishers.size()}</div>
                    <div class="stat-label">出版社总数</div>
                </div>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>📋 编号</th>
                            <th>🏢 出版社名称</th>
                            <th>👤 联系人</th>
                            <th>📱 手机号码</th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="publisher" items="${publishers}">
                            <tr>
                                <td class="publisher-id">#${publisher.pubId}</td>
                                <td class="publisher-name">${publisher.pubName}</td>
                                <td class="contact-person">${publisher.contacter}</td>
                                <td class="mobile-number">${publisher.mobile}</td>
                            </tr>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="footer">
            <p>© 2025 出版社信息管理系统 - Spring MVC + MyBatis 演示项目</p>
            <a href="${pageContext.request.contextPath}/" class="back-btn">🏠 返回首页</a>
        </div>
    </div>
</body>
</html>