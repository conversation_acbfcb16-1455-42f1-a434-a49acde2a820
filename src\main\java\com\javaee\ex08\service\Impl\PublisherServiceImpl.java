package com.javaee.ex08.service.Impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.javaee.ex08.mapper.PublisherMapper;
import com.javaee.ex08.po.Publisher;
import com.javaee.ex08.service.PublisherService;


@Service
@Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT, readOnly = false)
public class PublisherServiceImpl implements PublisherService
{
    @Autowired
    private PublisherMapper publisherMapper;

    @Override
    public List<Publisher> findPublishers()
    {
        return publisherMapper.findPublishers();
    }
}
