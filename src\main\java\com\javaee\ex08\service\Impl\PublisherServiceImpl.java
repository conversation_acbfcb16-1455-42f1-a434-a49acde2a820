package com.javaee.ex08.service.Impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.javaee.ex08.mapper.PublisherMapper;
import com.javaee.ex08.po.Publisher;
import com.javaee.ex08.service.PublisherService;


@Service
@Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT, readOnly = false)
public class PublisherServiceImpl implements PublisherService
{
    @Autowired
    private PublisherMapper publisherMapper;

    @Override
    public List<Publisher> findPublishers()
    {
        // 临时测试数据，避免数据库连接问题
        List<Publisher> publishers = new ArrayList<>();

        Publisher p1 = new Publisher();
        p1.setPubId(1);
        p1.setPubName("清华大学出版社");
        p1.setContacter("张三");
        publishers.add(p1);

        Publisher p2 = new Publisher();
        p2.setPubId(2);
        p2.setPubName("北京大学出版社");
        p2.setContacter("李四");
        publishers.add(p2);

        return publishers;
        // return publisherMapper.findPublishers();
    }
}
