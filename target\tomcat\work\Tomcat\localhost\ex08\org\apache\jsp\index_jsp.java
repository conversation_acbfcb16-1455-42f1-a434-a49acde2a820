/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-06-17 19:21:16 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;

public final class index_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html;charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"zh-CN\">\n");
      out.write("  <head>\n");
      out.write("    <meta charset=\"UTF-8\" />\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n");
      out.write("    <title>出版社管理系统 - 首页</title>\n");
      out.write("    <style>\n");
      out.write("      * {\n");
      out.write("        margin: 0;\n");
      out.write("        padding: 0;\n");
      out.write("        box-sizing: border-box;\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      body {\n");
      out.write("        font-family: \"Microsoft YaHei\", Arial, sans-serif;\n");
      out.write("        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("        min-height: 100vh;\n");
      out.write("        display: flex;\n");
      out.write("        align-items: center;\n");
      out.write("        justify-content: center;\n");
      out.write("        padding: 20px;\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      .welcome-container {\n");
      out.write("        background: white;\n");
      out.write("        border-radius: 20px;\n");
      out.write("        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);\n");
      out.write("        padding: 60px;\n");
      out.write("        text-align: center;\n");
      out.write("        max-width: 600px;\n");
      out.write("        width: 100%;\n");
      out.write("        animation: fadeInUp 1s ease;\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      .logo {\n");
      out.write("        font-size: 4em;\n");
      out.write("        margin-bottom: 20px;\n");
      out.write("        animation: bounce 2s infinite;\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      h1 {\n");
      out.write("        color: #333;\n");
      out.write("        font-size: 2.5em;\n");
      out.write("        margin-bottom: 15px;\n");
      out.write("        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("        -webkit-background-clip: text;\n");
      out.write("        -webkit-text-fill-color: transparent;\n");
      out.write("        background-clip: text;\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      .subtitle {\n");
      out.write("        color: #666;\n");
      out.write("        font-size: 1.2em;\n");
      out.write("        margin-bottom: 40px;\n");
      out.write("        line-height: 1.6;\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      .feature-grid {\n");
      out.write("        display: grid;\n");
      out.write("        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n");
      out.write("        gap: 20px;\n");
      out.write("        margin-bottom: 40px;\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      .feature-card {\n");
      out.write("        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n");
      out.write("        color: white;\n");
      out.write("        padding: 20px;\n");
      out.write("        border-radius: 15px;\n");
      out.write("        transition: transform 0.3s ease;\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      .feature-card:hover {\n");
      out.write("        transform: translateY(-5px);\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      .feature-icon {\n");
      out.write("        font-size: 2em;\n");
      out.write("        margin-bottom: 10px;\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      .feature-title {\n");
      out.write("        font-weight: bold;\n");
      out.write("        margin-bottom: 5px;\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      .feature-desc {\n");
      out.write("        font-size: 0.9em;\n");
      out.write("        opacity: 0.9;\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      .main-button {\n");
      out.write("        display: inline-block;\n");
      out.write("        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("        color: white;\n");
      out.write("        padding: 18px 40px;\n");
      out.write("        text-decoration: none;\n");
      out.write("        border-radius: 50px;\n");
      out.write("        font-size: 1.2em;\n");
      out.write("        font-weight: 600;\n");
      out.write("        transition: all 0.3s ease;\n");
      out.write("        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);\n");
      out.write("        text-transform: uppercase;\n");
      out.write("        letter-spacing: 1px;\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      .main-button:hover {\n");
      out.write("        transform: translateY(-3px);\n");
      out.write("        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      .tech-stack {\n");
      out.write("        margin-top: 40px;\n");
      out.write("        padding-top: 30px;\n");
      out.write("        border-top: 2px solid #eee;\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      .tech-title {\n");
      out.write("        color: #666;\n");
      out.write("        font-size: 1em;\n");
      out.write("        margin-bottom: 15px;\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      .tech-badges {\n");
      out.write("        display: flex;\n");
      out.write("        justify-content: center;\n");
      out.write("        gap: 10px;\n");
      out.write("        flex-wrap: wrap;\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      .tech-badge {\n");
      out.write("        background: #f8f9fa;\n");
      out.write("        color: #495057;\n");
      out.write("        padding: 8px 16px;\n");
      out.write("        border-radius: 20px;\n");
      out.write("        font-size: 0.9em;\n");
      out.write("        font-weight: 500;\n");
      out.write("        border: 2px solid #e9ecef;\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      @keyframes fadeInUp {\n");
      out.write("        from {\n");
      out.write("          opacity: 0;\n");
      out.write("          transform: translateY(50px);\n");
      out.write("        }\n");
      out.write("        to {\n");
      out.write("          opacity: 1;\n");
      out.write("          transform: translateY(0);\n");
      out.write("        }\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      @keyframes bounce {\n");
      out.write("        0%,\n");
      out.write("        20%,\n");
      out.write("        50%,\n");
      out.write("        80%,\n");
      out.write("        100% {\n");
      out.write("          transform: translateY(0);\n");
      out.write("        }\n");
      out.write("        40% {\n");
      out.write("          transform: translateY(-10px);\n");
      out.write("        }\n");
      out.write("        60% {\n");
      out.write("          transform: translateY(-5px);\n");
      out.write("        }\n");
      out.write("      }\n");
      out.write("\n");
      out.write("      .footer-info {\n");
      out.write("        margin-top: 30px;\n");
      out.write("        color: #999;\n");
      out.write("        font-size: 0.9em;\n");
      out.write("      }\n");
      out.write("    </style>\n");
      out.write("  </head>\n");
      out.write("\n");
      out.write("  <body>\n");
      out.write("    <div class=\"welcome-container\">\n");
      out.write("      <div class=\"logo\">📚</div>\n");
      out.write("      <h1>出版社管理系统</h1>\n");
      out.write("      <p class=\"subtitle\">\n");
      out.write("        基于 Spring MVC + MyBatis 架构的现代化出版社信息管理平台<br />\n");
      out.write("      </p>\n");
      out.write("\n");
      out.write("      <a href=\"publisher/findPublishers\" class=\"main-button\">\n");
      out.write("        🚀 查看出版社信息\n");
      out.write("      </a>\n");
      out.write("\n");
      out.write("      <div class=\"tech-stack\">\n");
      out.write("        <div class=\"tech-title\">技术栈展示</div>\n");
      out.write("        <div class=\"tech-badges\">\n");
      out.write("          <span class=\"tech-badge\">Spring MVC</span>\n");
      out.write("          <span class=\"tech-badge\">MyBatis</span>\n");
      out.write("          <span class=\"tech-badge\">MySQL</span>\n");
      out.write("          <span class=\"tech-badge\">JSP</span>\n");
      out.write("          <span class=\"tech-badge\">JSTL</span>\n");
      out.write("          <span class=\"tech-badge\">Maven</span>\n");
      out.write("        </div>\n");
      out.write("      </div>\n");
      out.write("\n");
      out.write("      <div class=\"footer-info\">© 2025 Spring MVC + MyBatis 演示项目</div>\n");
      out.write("    </div>\n");
      out.write("  </body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
