/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-06-17 20:18:26 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.pages.publisher;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;

public final class publisher_005flist_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems;

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.release();
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html;charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("<!DOCTYPE html>\r\n");
      out.write("<html lang=\"zh-CN\">\r\n");
      out.write("<head>\r\n");
      out.write("    <meta charset=\"UTF-8\">\r\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n");
      out.write("    <title>出版社信息管理系统</title>\r\n");
      out.write("    <style>\r\n");
      out.write("        * {\r\n");
      out.write("            margin: 0;\r\n");
      out.write("            padding: 0;\r\n");
      out.write("            box-sizing: border-box;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        body {\r\n");
      out.write("            font-family: 'Microsoft YaHei', Arial, sans-serif;\r\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n");
      out.write("            min-height: 100vh;\r\n");
      out.write("            padding: 20px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .container {\r\n");
      out.write("            max-width: 1000px;\r\n");
      out.write("            margin: 0 auto;\r\n");
      out.write("            background: white;\r\n");
      out.write("            border-radius: 15px;\r\n");
      out.write("            box-shadow: 0 20px 40px rgba(0,0,0,0.1);\r\n");
      out.write("            overflow: hidden;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .header {\r\n");
      out.write("            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n");
      out.write("            color: white;\r\n");
      out.write("            padding: 30px;\r\n");
      out.write("            text-align: center;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .header h1 {\r\n");
      out.write("            font-size: 2.5em;\r\n");
      out.write("            margin-bottom: 10px;\r\n");
      out.write("            text-shadow: 0 2px 4px rgba(0,0,0,0.3);\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .header p {\r\n");
      out.write("            font-size: 1.1em;\r\n");
      out.write("            opacity: 0.9;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .content {\r\n");
      out.write("            padding: 40px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .stats {\r\n");
      out.write("            display: flex;\r\n");
      out.write("            justify-content: center;\r\n");
      out.write("            margin-bottom: 30px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .stat-card {\r\n");
      out.write("            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);\r\n");
      out.write("            color: white;\r\n");
      out.write("            padding: 20px 30px;\r\n");
      out.write("            border-radius: 10px;\r\n");
      out.write("            text-align: center;\r\n");
      out.write("            box-shadow: 0 5px 15px rgba(0,0,0,0.1);\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .stat-number {\r\n");
      out.write("            font-size: 2em;\r\n");
      out.write("            font-weight: bold;\r\n");
      out.write("            margin-bottom: 5px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .stat-label {\r\n");
      out.write("            font-size: 0.9em;\r\n");
      out.write("            opacity: 0.9;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .table-container {\r\n");
      out.write("            background: white;\r\n");
      out.write("            border-radius: 10px;\r\n");
      out.write("            overflow: hidden;\r\n");
      out.write("            box-shadow: 0 5px 15px rgba(0,0,0,0.08);\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        table {\r\n");
      out.write("            width: 100%;\r\n");
      out.write("            border-collapse: collapse;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        thead {\r\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n");
      out.write("            color: white;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        th {\r\n");
      out.write("            padding: 20px;\r\n");
      out.write("            text-align: left;\r\n");
      out.write("            font-weight: 600;\r\n");
      out.write("            font-size: 1.1em;\r\n");
      out.write("            text-transform: uppercase;\r\n");
      out.write("            letter-spacing: 1px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        tbody tr {\r\n");
      out.write("            transition: all 0.3s ease;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        tbody tr:nth-child(even) {\r\n");
      out.write("            background-color: #f8f9ff;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        tbody tr:hover {\r\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n");
      out.write("            color: white;\r\n");
      out.write("            transform: scale(1.02);\r\n");
      out.write("            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        td {\r\n");
      out.write("            padding: 18px 20px;\r\n");
      out.write("            border-bottom: 1px solid #eee;\r\n");
      out.write("            font-size: 1em;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .publisher-id {\r\n");
      out.write("            font-weight: bold;\r\n");
      out.write("            color: #667eea;\r\n");
      out.write("            font-size: 1.1em;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        tbody tr:hover .publisher-id {\r\n");
      out.write("            color: white;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .publisher-name {\r\n");
      out.write("            font-weight: 600;\r\n");
      out.write("            color: #333;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        tbody tr:hover .publisher-name {\r\n");
      out.write("            color: white;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .contact-person {\r\n");
      out.write("            color: #666;\r\n");
      out.write("            font-style: italic;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        tbody tr:hover .contact-person {\r\n");
      out.write("            color: white;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .mobile-number {\r\n");
      out.write("            color: #28a745;\r\n");
      out.write("            font-weight: 500;\r\n");
      out.write("            font-family: 'Courier New', monospace;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        tbody tr:hover .mobile-number {\r\n");
      out.write("            color: white;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .footer {\r\n");
      out.write("            background: #f8f9fa;\r\n");
      out.write("            padding: 20px;\r\n");
      out.write("            text-align: center;\r\n");
      out.write("            color: #666;\r\n");
      out.write("            border-top: 1px solid #eee;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .back-btn {\r\n");
      out.write("            display: inline-block;\r\n");
      out.write("            margin-top: 20px;\r\n");
      out.write("            padding: 12px 30px;\r\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n");
      out.write("            color: white;\r\n");
      out.write("            text-decoration: none;\r\n");
      out.write("            border-radius: 25px;\r\n");
      out.write("            transition: all 0.3s ease;\r\n");
      out.write("            font-weight: 600;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .back-btn:hover {\r\n");
      out.write("            transform: translateY(-2px);\r\n");
      out.write("            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        @keyframes fadeInUp {\r\n");
      out.write("            from {\r\n");
      out.write("                opacity: 0;\r\n");
      out.write("                transform: translateY(30px);\r\n");
      out.write("            }\r\n");
      out.write("            to {\r\n");
      out.write("                opacity: 1;\r\n");
      out.write("                transform: translateY(0);\r\n");
      out.write("            }\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .container {\r\n");
      out.write("            animation: fadeInUp 0.8s ease;\r\n");
      out.write("        }\r\n");
      out.write("    </style>\r\n");
      out.write("</head>\r\n");
      out.write("\r\n");
      out.write("<body>\r\n");
      out.write("    <div class=\"container\">\r\n");
      out.write("        <div class=\"header\">\r\n");
      out.write("            <h1>📚出版社信息管理系统</h1>\r\n");
      out.write("            <p>Publisher Information Management System</p>\r\n");
      out.write("        </div>\r\n");
      out.write("\r\n");
      out.write("        <div class=\"content\">\r\n");
      out.write("            <div class=\"stats\">\r\n");
      out.write("                <div class=\"stat-card\">\r\n");
      out.write("                    <div class=\"stat-number\">");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${publishers.size()}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("</div>\r\n");
      out.write("                    <div class=\"stat-label\">出版社总数</div>\r\n");
      out.write("                </div>\r\n");
      out.write("            </div>\r\n");
      out.write("\r\n");
      out.write("            <div class=\"table-container\">\r\n");
      out.write("                <table>\r\n");
      out.write("                    <thead>\r\n");
      out.write("                        <tr>\r\n");
      out.write("                            <th>📋 编号</th>\r\n");
      out.write("                            <th>🏢 出版社名称</th>\r\n");
      out.write("                            <th>👤 联系人</th>\r\n");
      out.write("                            <th>📱 手机号码</th>\r\n");
      out.write("                        </tr>\r\n");
      out.write("                    </thead>\r\n");
      out.write("                    <tbody>\r\n");
      out.write("                        ");
      if (_jspx_meth_c_005fforEach_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("                    </tbody>\r\n");
      out.write("                </table>\r\n");
      out.write("            </div>\r\n");
      out.write("        </div>\r\n");
      out.write("\r\n");
      out.write("        <div class=\"footer\">\r\n");
      out.write("            <p>© 2025 出版社信息管理系统 - Spring MVC + MyBatis 演示项目</p>\r\n");
      out.write("            <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/\" class=\"back-btn\">🏠 返回首页</a>\r\n");
      out.write("        </div>\r\n");
      out.write("    </div>\r\n");
      out.write("</body>\r\n");
      out.write("</html>");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_c_005fforEach_005f0(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f0 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    _jspx_th_c_005fforEach_005f0.setPageContext(_jspx_page_context);
    _jspx_th_c_005fforEach_005f0.setParent(null);
    // /WEB-INF/pages/publisher/publisher_list.jsp(234,24) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
    _jspx_th_c_005fforEach_005f0.setVar("publisher");
    // /WEB-INF/pages/publisher/publisher_list.jsp(234,24) name = items type = javax.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
    _jspx_th_c_005fforEach_005f0.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/pages/publisher/publisher_list.jsp(234,24) '${publishers}'",_el_expressionfactory.createValueExpression(_jspx_page_context.getELContext(),"${publishers}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
    int[] _jspx_push_body_count_c_005fforEach_005f0 = new int[] { 0 };
    try {
      int _jspx_eval_c_005fforEach_005f0 = _jspx_th_c_005fforEach_005f0.doStartTag();
      if (_jspx_eval_c_005fforEach_005f0 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                            <tr>\r\n");
          out.write("                                <td class=\"publisher-id\">#");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${publisher.pubId}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
          out.write("</td>\r\n");
          out.write("                                <td class=\"publisher-name\">");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${publisher.pubName}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
          out.write("</td>\r\n");
          out.write("                                <td class=\"contact-person\">");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${publisher.contacter}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
          out.write("</td>\r\n");
          out.write("                                <td class=\"mobile-number\">");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${publisher.mobile}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
          out.write("</td>\r\n");
          out.write("                            </tr>\r\n");
          out.write("                        ");
          int evalDoAfterBody = _jspx_th_c_005fforEach_005f0.doAfterBody();
          if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fforEach_005f0.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
    } catch (java.lang.Throwable _jspx_exception) {
      while (_jspx_push_body_count_c_005fforEach_005f0[0]-- > 0)
        out = _jspx_page_context.popBody();
      _jspx_th_c_005fforEach_005f0.doCatch(_jspx_exception);
    } finally {
      _jspx_th_c_005fforEach_005f0.doFinally();
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f0);
    }
    return false;
  }
}
