<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>出版社管理系统 - 首页</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      .welcome-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
        padding: 60px;
        text-align: center;
        max-width: 600px;
        width: 100%;
        animation: fadeInUp 1s ease;
      }

      .logo {
        font-size: 4em;
        margin-bottom: 20px;
        animation: bounce 2s infinite;
      }

      h1 {
        color: #333;
        font-size: 2.5em;
        margin-bottom: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .subtitle {
        color: #666;
        font-size: 1.2em;
        margin-bottom: 40px;
        line-height: 1.6;
      }

      .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
      }

      .feature-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 20px;
        border-radius: 15px;
        transition: transform 0.3s ease;
      }

      .feature-card:hover {
        transform: translateY(-5px);
      }

      .feature-icon {
        font-size: 2em;
        margin-bottom: 10px;
      }

      .feature-title {
        font-weight: bold;
        margin-bottom: 5px;
      }

      .feature-desc {
        font-size: 0.9em;
        opacity: 0.9;
      }

      .main-button {
        display: inline-block;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 18px 40px;
        text-decoration: none;
        border-radius: 50px;
        font-size: 1.2em;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .main-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);
      }

      .tech-stack {
        margin-top: 40px;
        padding-top: 30px;
        border-top: 2px solid #eee;
      }

      .tech-title {
        color: #666;
        font-size: 1em;
        margin-bottom: 15px;
      }

      .tech-badges {
        display: flex;
        justify-content: center;
        gap: 10px;
        flex-wrap: wrap;
      }

      .tech-badge {
        background: #f8f9fa;
        color: #495057;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9em;
        font-weight: 500;
        border: 2px solid #e9ecef;
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(50px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes bounce {
        0%,
        20%,
        50%,
        80%,
        100% {
          transform: translateY(0);
        }
        40% {
          transform: translateY(-10px);
        }
        60% {
          transform: translateY(-5px);
        }
      }

      .footer-info {
        margin-top: 30px;
        color: #999;
        font-size: 0.9em;
      }
    </style>
  </head>

  <body>
    <div class="welcome-container">
      <div class="logo">📚</div>
      <h1>出版社管理系统</h1>
      <p class="subtitle">
        基于 Spring MVC + MyBatis 架构的现代化出版社信息管理平台<br />
      </p>

      <a href="publisher/findPublishers" class="main-button">
        🚀 查看出版社信息
      </a>

      <div class="tech-stack">
        <div class="tech-title">技术栈展示</div>
        <div class="tech-badges">
          <span class="tech-badge">Spring MVC</span>
          <span class="tech-badge">MyBatis</span>
          <span class="tech-badge">MySQL</span>
          <span class="tech-badge">JSP</span>
          <span class="tech-badge">JSTL</span>
          <span class="tech-badge">Maven</span>
        </div>
      </div>

      <div class="footer-info">© 2025 Spring MVC + MyBatis 演示项目</div>
    </div>
  </body>
</html>
